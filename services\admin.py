from django.contrib import admin
from .models import Offer, Subscription, ContactRequest, Payment


@admin.register(Offer)
class OfferAdmin(admin.ModelAdmin):
    list_display = ('title', 'hiring_partner', 'price', 'is_premium', 'is_active')
    list_filter = ('is_premium', 'is_active', 'created_at')
    search_fields = ('title', 'description', 'hiring_partner__company_name')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = ('hiring_partner', 'plan', 'start_date', 'end_date', 'status')
    list_filter = ('plan', 'status', 'start_date')
    search_fields = ('hiring_partner__company_name', 'hiring_partner__user__email')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(ContactRequest)
class ContactRequestAdmin(admin.ModelAdmin):
    list_display = ('sender', 'recipient', 'status', 'sent_at', 'responded_at')
    list_filter = ('status', 'sent_at')
    search_fields = (
        'sender__company_name',
        'recipient__user__username',
        'message'
    )
    readonly_fields = ('sent_at',)


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = (
        'transaction_id',
        'subscription',
        'amount',
        'status',
        'created_at'
    )
    list_filter = ('status', 'created_at')
    search_fields = (
        'transaction_id',
        'subscription__hiring_partner__company_name'
    )
    readonly_fields = ('created_at',)
