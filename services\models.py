from django.db import models
from django.contrib.auth import get_user_model
from users.models import ApplicantProfile, HiringPartnerProfile
from django.utils import timezone

User = get_user_model()


class Offer(models.Model):
    """Model representing a service offer."""
    title = models.CharField(max_length=200)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    is_premium = models.BooleanField(default=False)
    hiring_partner = models.ForeignKey(
        HiringPartnerProfile, on_delete=models.CASCADE, related_name="offers"
    )
    is_active = models.<PERSON><PERSON>anField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Offer'
        verbose_name_plural = 'Offers'
        ordering = ['-created_at']

    def __str__(self):
        return self.title


class Subscription(models.Model):
    """Model representing a subscription to a service."""
    hiring_partner = models.ForeignKey(
        HiringPartnerProfile, on_delete=models.CASCADE, related_name="subscriptions"
    )
    plan = models.CharField(
        max_length=20,
        choices=[
            ('basic', 'Basic'),
            ('standard', 'Standard'),
            ('premium', 'Premium'),
            ('enterprise', 'Enterprise')
        ]
    )
    start_date = models.DateField()
    end_date = models.DateField(blank=True, null=True)
    status = models.CharField(
        max_length=20,
        choices=[
            ('active', 'Active'),
            ('cancelled', 'Cancelled'),
            ('expired', 'Expired'),
            ('pending', 'Pending')
        ],
        default='pending'
    )
    is_auto_renew = models.BooleanField(default=True)
    payment_method = models.CharField(max_length=100, blank=True, null=True)
    # Stripe-specific fields
    stripe_customer_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_subscription_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_payment_method_id = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Subscription'
        verbose_name_plural = 'Subscriptions'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.hiring_partner.company_name} - {self.plan} Plan"


class ContactRequest(models.Model):
    """Model representing contact requests between hiring partners and applicants."""
    sender = models.ForeignKey(
        HiringPartnerProfile, on_delete=models.CASCADE, related_name="sent_requests"
    )
    recipient = models.ForeignKey(
        ApplicantProfile, on_delete=models.CASCADE, related_name="received_requests"
    )
    message = models.TextField()
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('accepted', 'Accepted'),
            ('declined', 'Declined')
        ],
        default='pending'
    )
    sent_at = models.DateTimeField(auto_now_add=True)
    responded_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        verbose_name = 'Contact Request'
        verbose_name_plural = 'Contact Requests'
        ordering = ['-sent_at']

    def __str__(self):
        return f"Request from {self.sender.company_name} to {self.recipient.user.username}"


class Payment(models.Model):
    """Model representing payments for subscriptions."""
    subscription = models.ForeignKey(
        Subscription, on_delete=models.CASCADE, related_name="payments"
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('succeeded', 'Succeeded'),
            ('failed', 'Failed'),
            ('refunded', 'Refunded')
        ]
    )
    transaction_id = models.CharField(max_length=100, unique=True)
    payment_method = models.CharField(max_length=50, blank=True, null=True)
    billing_details = models.JSONField(blank=True, null=True)
    # Stripe-specific fields
    stripe_payment_intent_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_charge_id = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Payment'
        verbose_name_plural = 'Payments'
        ordering = ['-created_at']

    def __str__(self):
        return f"Payment {self.transaction_id} - {self.amount}"


class PipelineStage(models.Model):
    """Model representing recruitment pipeline stages."""
    STAGE_CHOICES = [
        ('new', 'New'),
        ('screening', 'Screening'),
        ('interview', 'Interview'),
        ('offer', 'Offer'),
        ('hired', 'Hired'),
        ('rejected', 'Rejected'),
    ]

    name = models.CharField(max_length=50, choices=STAGE_CHOICES, unique=True)
    display_name = models.CharField(max_length=100)
    order = models.IntegerField(default=0)
    color = models.CharField(max_length=7, default='#6c757d')  # Hex color
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order']
        verbose_name = 'Pipeline Stage'
        verbose_name_plural = 'Pipeline Stages'

    def __str__(self):
        return self.display_name


class CandidateStageHistory(models.Model):
    """Model for tracking candidate movement through pipeline stages."""
    candidate = models.ForeignKey(
        ApplicantProfile,
        on_delete=models.CASCADE,
        related_name="stage_history"
    )
    hiring_partner = models.ForeignKey(
        HiringPartnerProfile,
        on_delete=models.CASCADE,
        related_name="candidate_stage_changes"
    )
    stage = models.ForeignKey(
        PipelineStage,
        on_delete=models.CASCADE,
        related_name="candidate_entries"
    )
    previous_stage = models.ForeignKey(
        PipelineStage,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="candidate_exits"
    )
    notes = models.TextField(blank=True, null=True)
    changed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    changed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-changed_at']
        verbose_name = 'Candidate Stage History'
        verbose_name_plural = 'Candidate Stage Histories'

    def __str__(self):
        return f"{self.candidate.user.get_full_name()} -> {self.stage.display_name}"


class CandidatePipeline(models.Model):
    """Model representing a candidate's current position in a hiring partner's pipeline."""
    candidate = models.ForeignKey(
        ApplicantProfile,
        on_delete=models.CASCADE,
        related_name="pipeline_positions"
    )
    hiring_partner = models.ForeignKey(
        HiringPartnerProfile,
        on_delete=models.CASCADE,
        related_name="pipeline_candidates"
    )
    current_stage = models.ForeignKey(
        PipelineStage,
        on_delete=models.CASCADE,
        related_name="current_candidates"
    )
    added_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    priority = models.CharField(
        max_length=10,
        choices=[
            ('low', 'Low'),
            ('medium', 'Medium'),
            ('high', 'High'),
        ],
        default='medium'
    )
    notes = models.TextField(blank=True, null=True)

    class Meta:
        unique_together = ['candidate', 'hiring_partner']
        ordering = ['-updated_at']
        verbose_name = 'Candidate Pipeline'
        verbose_name_plural = 'Candidate Pipelines'

    def __str__(self):
        return f"{self.candidate.user.get_full_name()} - {self.current_stage.display_name}"


class CommunicationType(models.Model):
    """Model for different types of communication."""
    COMMUNICATION_TYPES = [
        ('email', 'Email'),
        ('phone', 'Phone Call'),
        ('meeting', 'Meeting'),
        ('message', 'Platform Message'),
        ('interview', 'Interview'),
        ('note', 'Internal Note'),
    ]

    name = models.CharField(max_length=20, choices=COMMUNICATION_TYPES, unique=True)
    display_name = models.CharField(max_length=50)
    icon = models.CharField(max_length=20, default='bi-chat')
    color = models.CharField(max_length=7, default='#6c757d')
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = 'Communication Type'
        verbose_name_plural = 'Communication Types'

    def __str__(self):
        return self.display_name


class CommunicationLog(models.Model):
    """Model for tracking all communications with candidates."""
    candidate = models.ForeignKey(
        ApplicantProfile,
        on_delete=models.CASCADE,
        related_name="communications"
    )
    hiring_partner = models.ForeignKey(
        HiringPartnerProfile,
        on_delete=models.CASCADE,
        related_name="communications_sent"
    )
    communication_type = models.ForeignKey(
        CommunicationType,
        on_delete=models.CASCADE,
        related_name="communications"
    )
    subject = models.CharField(max_length=200, blank=True, null=True)
    content = models.TextField()
    scheduled_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    # Follow-up tracking
    requires_follow_up = models.BooleanField(default=False)
    follow_up_date = models.DateTimeField(null=True, blank=True)
    follow_up_completed = models.BooleanField(default=False)

    # Metadata
    external_id = models.CharField(max_length=100, blank=True, null=True)  # For email/calendar IDs
    attachments = models.JSONField(default=list, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Communication Log'
        verbose_name_plural = 'Communication Logs'

    def __str__(self):
        return f"{self.communication_type.display_name} with {self.candidate.user.get_full_name()}"


class CommunicationTemplate(models.Model):
    """Model for communication templates."""
    TEMPLATE_TYPES = [
        ('email', 'Email Template'),
        ('message', 'Message Template'),
        ('interview_invite', 'Interview Invitation'),
        ('offer_letter', 'Offer Letter'),
        ('rejection', 'Rejection Letter'),
        ('follow_up', 'Follow-up Message'),
    ]

    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)
    subject_template = models.CharField(max_length=200, blank=True, null=True)
    content_template = models.TextField()
    hiring_partner = models.ForeignKey(
        HiringPartnerProfile,
        on_delete=models.CASCADE,
        related_name="communication_templates",
        null=True,
        blank=True
    )
    is_global = models.BooleanField(default=False)  # Available to all hiring partners
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Template variables help
    available_variables = models.JSONField(
        default=list,
        help_text="Available template variables like {candidate_name}, {company_name}, etc."
    )

    class Meta:
        ordering = ['template_type', 'name']
        verbose_name = 'Communication Template'
        verbose_name_plural = 'Communication Templates'

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"


class FollowUpReminder(models.Model):
    """Model for automated follow-up reminders."""
    REMINDER_TYPES = [
        ('email', 'Email Reminder'),
        ('platform', 'Platform Notification'),
        ('calendar', 'Calendar Event'),
    ]

    REMINDER_STATUS = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    candidate = models.ForeignKey(
        ApplicantProfile,
        on_delete=models.CASCADE,
        related_name="follow_up_reminders"
    )
    hiring_partner = models.ForeignKey(
        HiringPartnerProfile,
        on_delete=models.CASCADE,
        related_name="follow_up_reminders"
    )
    communication_log = models.ForeignKey(
        CommunicationLog,
        on_delete=models.CASCADE,
        related_name="follow_up_reminders",
        null=True,
        blank=True
    )

    reminder_type = models.CharField(max_length=20, choices=REMINDER_TYPES, default='platform')
    status = models.CharField(max_length=20, choices=REMINDER_STATUS, default='pending')

    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    scheduled_for = models.DateTimeField()
    completed_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    class Meta:
        ordering = ['scheduled_for']
        verbose_name = 'Follow-up Reminder'
        verbose_name_plural = 'Follow-up Reminders'

    def __str__(self):
        return f"Follow-up: {self.title} ({self.scheduled_for.strftime('%Y-%m-%d')})"


# AI Matching Models

class JobRequirement(models.Model):
    """Model for defining job requirements and matching criteria."""
    offer = models.OneToOneField(
        Offer,
        on_delete=models.CASCADE,
        related_name="requirements"
    )

    # Required skills (JSON array)
    required_skills = models.JSONField(default=list, help_text="List of required skills")
    preferred_skills = models.JSONField(default=list, help_text="List of preferred skills")

    # Experience requirements
    min_experience_years = models.IntegerField(default=0)
    max_experience_years = models.IntegerField(null=True, blank=True)

    # Location preferences
    preferred_locations = models.JSONField(default=list, help_text="List of preferred locations")
    remote_work_allowed = models.BooleanField(default=False)

    # Education and languages
    required_languages = models.JSONField(default=list, help_text="List of required languages")
    education_level = models.CharField(max_length=50, blank=True, null=True)

    # Scoring weights (0-100)
    skills_weight = models.IntegerField(default=40, help_text="Weight for skills matching (0-100)")
    experience_weight = models.IntegerField(default=30, help_text="Weight for experience matching (0-100)")
    location_weight = models.IntegerField(default=20, help_text="Weight for location matching (0-100)")
    language_weight = models.IntegerField(default=10, help_text="Weight for language matching (0-100)")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Requirements for {self.offer.title}"


class CandidateScore(models.Model):
    """Model for storing candidate matching scores for specific jobs."""
    candidate = models.ForeignKey(
        'users.ApplicantProfile',
        on_delete=models.CASCADE,
        related_name="job_scores"
    )
    offer = models.ForeignKey(
        Offer,
        on_delete=models.CASCADE,
        related_name="candidate_scores"
    )
    hiring_partner = models.ForeignKey(
        HiringPartnerProfile,
        on_delete=models.CASCADE,
        related_name="candidate_scores"
    )

    # Overall matching score (0-100)
    overall_score = models.FloatField(default=0.0)

    # Individual component scores (0-100)
    skills_score = models.FloatField(default=0.0)
    experience_score = models.FloatField(default=0.0)
    location_score = models.FloatField(default=0.0)
    language_score = models.FloatField(default=0.0)

    # Matching details
    matched_skills = models.JSONField(default=list)
    missing_skills = models.JSONField(default=list)
    skill_gaps = models.JSONField(default=list)

    # AI enhancement fields (for future use)
    ai_score = models.FloatField(null=True, blank=True)
    ai_reasoning = models.TextField(blank=True, null=True)

    # Quality indicators
    quality_rating = models.CharField(
        max_length=20,
        choices=[
            ('excellent', 'Excellent Match'),
            ('good', 'Good Match'),
            ('fair', 'Fair Match'),
            ('poor', 'Poor Match'),
        ],
        default='fair'
    )

    # Metadata
    calculated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['candidate', 'offer']
        ordering = ['-overall_score', '-calculated_at']

    def __str__(self):
        return (f"{self.candidate.user.get_full_name()} - "
                f"{self.offer.title} ({self.overall_score:.1f}%)")


class MatchingCriteria(models.Model):
    """Model for custom matching criteria per hiring partner."""
    hiring_partner = models.OneToOneField(
        HiringPartnerProfile,
        on_delete=models.CASCADE,
        related_name="matching_criteria"
    )

    # Global scoring preferences
    default_skills_weight = models.IntegerField(default=40)
    default_experience_weight = models.IntegerField(default=30)
    default_location_weight = models.IntegerField(default=20)
    default_language_weight = models.IntegerField(default=10)

    # Matching preferences
    strict_skill_matching = models.BooleanField(default=False)
    allow_junior_candidates = models.BooleanField(default=True)
    prioritize_local_candidates = models.BooleanField(default=False)

    # AI preferences (for future use)
    use_ai_scoring = models.BooleanField(default=False)
    ai_model_preference = models.CharField(
        max_length=20,
        choices=[
            ('openai', 'OpenAI GPT'),
            ('claude', 'Anthropic Claude'),
            ('local', 'Local Model'),
        ],
        default='openai',
        blank=True,
        null=True
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Matching criteria for {self.hiring_partner.company_name}"
