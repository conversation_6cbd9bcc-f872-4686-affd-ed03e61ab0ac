from django.urls import path
from . import views

app_name = 'services'

urlpatterns = [
    path('', views.service_list, name='index'),
    path('list/', views.service_list, name='list'),
    path('dashboard/', views.service_dashboard, name='dashboard'),
    path('register/', views.service_registration, name='register'),
    path('profile/', views.service_profile, name='profile'),
    path('browser/', views.service_browser, name='browser'),
    path('account/', views.service_account, name='account'),
    path('subscription/', views.subscription_management, name='subscription'),
    path('contact-request/', views.contact_request, name='contact_request'),
    path('contact-requests/', views.contact_request_list, name='contact_requests'),
    path('contact-request/<int:request_id>/', views.contact_request_detail, name='contact_request_detail'),
    path('contact-request/<int:request_id>/cancel/', views.contact_request_cancel, name='contact_request_cancel'),
    path('offers/', views.offer_list, name='offers'),
    path('offers/create/', views.create_offer, name='create_offer'),
    path('offers/<int:offer_id>/edit/', views.edit_offer, name='edit_offer'),
    path('offers/<int:offer_id>/delete/', views.delete_offer, name='delete_offer'),
    path('applicants/', views.applicant_list, name='applicants'),
    path('applicants/<int:applicant_id>/', views.applicant_detail, name='applicant_detail'),
    # Kanban Pipeline URLs
    path('pipeline/', views.candidate_pipeline, name='pipeline'),
    path('pipeline/move-candidate/', views.move_candidate_stage, name='move_candidate_stage'),
    path('pipeline/add-candidate/', views.add_candidate_to_pipeline, name='add_candidate_to_pipeline'),
    # Communication API URLs
    path('api/communication/save/', views.save_communication, name='save_communication'),
    path('api/communication/history/<int:candidate_id>/', views.get_communication_history, name='get_communication_history'),
    path('api/communication/follow-up/<int:communication_id>/', views.update_follow_up, name='update_follow_up'),
    # Analytics Dashboard URLs
    path('analytics/', views.analytics_dashboard, name='analytics_dashboard'),
    path('faq/', views.faq, name='faq'),
    path('support/', views.support, name='support'),
    path('payment/', views.payment_process, name='payment'),
    path('payment/success/', views.payment_success, name='payment_success'),
    path('payment/cancel/', views.payment_cancel, name='payment_cancel'),
    path('webhook/', views.stripe_webhook, name='webhook'),
    path('subscription/cancel/<int:subscription_id>/', views.cancel_subscription, name='cancel_subscription'),
    path('subscription/renew/<int:subscription_id>/', views.renew_subscription, name='renew_subscription'),
    path('subscription/change-plan/', views.change_plan, name='change_plan'),
]
