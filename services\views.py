from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.core.paginator import Paginator
from django.db.models import Q
from django.urls import reverse
import stripe
from django.utils import timezone
import json

from users.models import HiringPartnerProfile
from .models import (
    Offer, Subscription, ContactRequest, Payment,
    PipelineStage, CandidatePipeline, CandidateStageHistory,
    CommunicationType, CommunicationLog, FollowUpReminder, CommunicationTemplate
)
from .forms import Hiring<PERSON>artnerForm, OfferForm, ContactRequestForm
from application.models import ApplicantProfile
from application.models import RefinedInfo
from .stripe_service import StripeService

# Import the email notification service
try:
    from notifications.utils import EmailNotificationService
    email_service_available = True
except ImportError:
    email_service_available = False

stripe.api_key = settings.STRIPE_SECRET_KEY


def service_list(request):
    """View for listing services/offers with search and filtering functionality."""
    # Start with all active offers
    offers = Offer.objects.filter(is_active=True)

    # Get filter parameters
    industry = request.GET.get('industry')
    location = request.GET.get('location')
    price_min = request.GET.get('price_min')
    price_max = request.GET.get('price_max')
    is_premium = request.GET.get('is_premium')
    search_query = request.GET.get('q')

    # Apply filters
    if industry:
        offers = offers.filter(hiring_partner__industry=industry)

    if location:
        offers = offers.filter(
            Q(hiring_partner__primary_location__icontains=location) |
            Q(hiring_partner__company_city__icontains=location) |
            Q(hiring_partner__company_state__icontains=location)
        )

    if price_min:
        try:
            price_min = float(price_min)
            offers = offers.filter(price__gte=price_min)
        except (ValueError, TypeError):
            pass

    if price_max:
        try:
            price_max = float(price_max)
            offers = offers.filter(price__lte=price_max)
        except (ValueError, TypeError):
            pass

    if is_premium:
        offers = offers.filter(is_premium=True)

    # Apply search query
    if search_query:
        offers = offers.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(hiring_partner__company_name__icontains=search_query) |
            Q(hiring_partner__services_offered__icontains=search_query)
        )

    # Get unique industries and locations for filter dropdowns
    industries = HiringPartnerProfile.objects.values_list('industry', flat=True).distinct()
    locations = HiringPartnerProfile.objects.values_list('primary_location', flat=True).distinct()

    # Paginate the results
    paginator = Paginator(offers, 6)  # Show 6 offers per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'services/list.html', {
        'page_obj': page_obj,
        'total_offers': offers.count(),
        'industries': industries,
        'locations': locations,
        'selected_industry': industry,
        'selected_location': location,
        'price_min': price_min,
        'price_max': price_max,
        'is_premium': is_premium,
        'search_query': search_query,
    })


@login_required
def service_dashboard(request):
    """Dashboard for hiring partners."""
    if not hasattr(request.user, 'hiring_partner_profile'):
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')

    hiring_partner_profile = request.user.hiring_partner_profile

    # Get active subscription
    subscription = Subscription.objects.filter(
        hiring_partner=hiring_partner_profile,
        status='active'
    ).first()

    # Get contact requests
    contact_requests = ContactRequest.objects.filter(sender=hiring_partner_profile)
    recent_contact_requests = contact_requests.order_by('-sent_at')[:5]
    pending_requests = contact_requests.filter(status='pending').count()
    total_requests = contact_requests.count()

    # Get current month's requests count for progress bar
    current_month = timezone.now().month
    current_year = timezone.now().year
    current_requests = contact_requests.filter(
        sent_at__month=current_month,
        sent_at__year=current_year
    ).count()

    # Get offers
    service_offers = Offer.objects.filter(hiring_partner=hiring_partner_profile)
    active_offers = service_offers.filter(is_active=True).count()
    recent_offers = service_offers.order_by('-created_at')[:3]

    # Get recommended applicants (based on industry match)
    recommended_applicants = []
    if subscription and hiring_partner_profile.industry:
        recommended_applicants = ApplicantProfile.objects.filter(
            industry=hiring_partner_profile.industry
        ).order_by('?')[:3]  # Random selection of matching applicants

    # Mock profile views for now (would be implemented with analytics in production)
    profile_views = 42

    # Calculate profile completion percentage
    completion_percentage = calculate_profile_completion(hiring_partner_profile)

    context = {
        'profile': hiring_partner_profile,
        'subscription': subscription,
        'recent_contact_requests': recent_contact_requests,
        'pending_requests': pending_requests,
        'total_requests': total_requests,
        'current_requests': current_requests,
        'service_offers': recent_offers,
        'active_offers': active_offers,
        'recommended_applicants': recommended_applicants,
        'profile_views': profile_views,
        'completion_percentage': completion_percentage
    }

    return render(request, 'services/dashboard.html', context)


@login_required
def candidate_pipeline(request):
    """Kanban-style candidate pipeline view for recruiters."""
    if not hasattr(request.user, 'hiring_partner_profile'):
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')

    hiring_partner_profile = request.user.hiring_partner_profile

    # Get all pipeline stages
    stages = PipelineStage.objects.filter(is_active=True)

    # Get candidates in pipeline for this hiring partner
    pipeline_candidates = CandidatePipeline.objects.filter(
        hiring_partner=hiring_partner_profile
    ).select_related('candidate', 'current_stage')

    # Organize candidates by stage
    candidates_by_stage = {}
    for stage in stages:
        candidates_by_stage[stage.name] = []

    for pipeline_candidate in pipeline_candidates:
        stage_name = pipeline_candidate.current_stage.name
        if stage_name in candidates_by_stage:
            candidates_by_stage[stage_name].append(pipeline_candidate)

    # Get all applicants not yet in pipeline (for adding new candidates)
    candidates_in_pipeline = pipeline_candidates.values_list('candidate_id', flat=True)
    available_candidates = ApplicantProfile.objects.exclude(
        id__in=candidates_in_pipeline
    )[:20]  # Limit to 20 for performance

    context = {
        'stages': stages,
        'candidates_by_stage': candidates_by_stage,
        'available_candidates': available_candidates,
        'hiring_partner': hiring_partner_profile,
    }

    return render(request, 'services/candidate_pipeline.html', context)


@login_required
def move_candidate_stage(request):
    """AJAX endpoint for moving candidates between pipeline stages."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Invalid method'})

    if not hasattr(request.user, 'hiring_partner_profile'):
        return JsonResponse({'success': False, 'error': 'No hiring partner profile'})

    try:
        candidate_id = request.POST.get('candidate_id')
        new_stage_id = request.POST.get('stage_id')
        notes = request.POST.get('notes', '')

        hiring_partner_profile = request.user.hiring_partner_profile
        candidate = get_object_or_404(ApplicantProfile, id=candidate_id)
        new_stage = get_object_or_404(PipelineStage, id=new_stage_id)

        # Get or create pipeline entry
        pipeline_candidate, created = CandidatePipeline.objects.get_or_create(
            candidate=candidate,
            hiring_partner=hiring_partner_profile,
            defaults={'current_stage': new_stage, 'notes': notes}
        )

        if not created:
            # Record the stage change in history
            CandidateStageHistory.objects.create(
                candidate=candidate,
                hiring_partner=hiring_partner_profile,
                stage=new_stage,
                previous_stage=pipeline_candidate.current_stage,
                notes=notes,
                changed_by=request.user
            )

            # Update current stage
            pipeline_candidate.current_stage = new_stage
            if notes:
                pipeline_candidate.notes = notes
            pipeline_candidate.save()
        else:
            # Record initial stage entry
            CandidateStageHistory.objects.create(
                candidate=candidate,
                hiring_partner=hiring_partner_profile,
                stage=new_stage,
                notes=notes,
                changed_by=request.user
            )

        return JsonResponse({
            'success': True,
            'message': f'Candidate moved to {new_stage.display_name}'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def add_candidate_to_pipeline(request):
    """AJAX endpoint for adding new candidates to pipeline."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Invalid method'})

    if not hasattr(request.user, 'hiring_partner_profile'):
        return JsonResponse({'success': False, 'error': 'No hiring partner profile'})

    try:
        candidate_id = request.POST.get('candidate_id')
        notes = request.POST.get('notes', '')

        hiring_partner_profile = request.user.hiring_partner_profile
        candidate = get_object_or_404(ApplicantProfile, id=candidate_id)

        # Get the "new" stage (first stage)
        new_stage = PipelineStage.objects.filter(name='new').first()
        if not new_stage:
            return JsonResponse({'success': False, 'error': 'No "new" stage found'})

        # Check if candidate is already in pipeline
        if CandidatePipeline.objects.filter(
            candidate=candidate,
            hiring_partner=hiring_partner_profile
        ).exists():
            return JsonResponse({'success': False, 'error': 'Candidate already in pipeline'})

        # Add candidate to pipeline
        pipeline_candidate = CandidatePipeline.objects.create(
            candidate=candidate,
            hiring_partner=hiring_partner_profile,
            current_stage=new_stage,
            notes=notes
        )

        # Record in history
        CandidateStageHistory.objects.create(
            candidate=candidate,
            hiring_partner=hiring_partner_profile,
            stage=new_stage,
            notes=notes,
            changed_by=request.user
        )

        return JsonResponse({
            'success': True,
            'message': f'Candidate added to pipeline',
            'candidate_name': candidate.user.get_full_name() or candidate.user.username
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def service_registration(request):
    """View for hiring partner registration."""
    # Check if the user already has a hiring partner profile
    if hasattr(request.user, 'hiring_partner_profile'):
        messages.info(request, "You already have a hiring partner profile.")
        return redirect('services:dashboard')

    # Handle form submission
    if request.method == 'POST':
        form = HiringPartnerForm(request.POST, request.FILES)
        if form.is_valid():
            # Create a new hiring partner profile
            hiring_partner_profile = form.save(commit=False)
            hiring_partner_profile.user = request.user
            hiring_partner_profile.save()

            # Update user type to service if it's not already set
            # For users with multiple profiles, we'll set it to the current active one
            request.user.user_type = 'service'
            request.user.save(update_fields=['user_type'])

            messages.success(request, "Your hiring partner profile has been created successfully.")
            return redirect('services:subscription')
    else:
        # Pre-fill form with user data if available
        initial_data = {}
        if request.user.first_name and request.user.last_name:
            initial_data['company_name'] = f"{request.user.first_name} {request.user.last_name}'s Company"

        form = HiringPartnerForm(initial=initial_data)

    return render(request, 'services/register.html', {'form': form})


@login_required
def subscription_management(request):
    """View for managing subscriptions."""
    if not hasattr(request.user, 'hiring_partner_profile'):
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')

    hiring_partner_profile = request.user.hiring_partner_profile
    subscriptions = Subscription.objects.filter(hiring_partner=hiring_partner_profile).order_by('-created_at')

    return render(request, 'services/subscription.html', {
        'subscriptions': subscriptions
    })


@login_required
def contact_request(request):
    """View for creating contact requests."""
    if not hasattr(request.user, 'hiring_partner_profile'):
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')

    hiring_partner_profile = request.user.hiring_partner_profile

    if request.method == 'POST':
        applicant_id = request.POST.get('applicant_id')
        message = request.POST.get('message')

        try:
            applicant = ApplicantProfile.objects.get(id=applicant_id)

            # Create contact request
            contact_req = ContactRequest(
                sender=hiring_partner_profile,
                recipient=applicant,
                message=message
            )
            contact_req.save()

            # Send email notification if the service is available
            if email_service_available:
                try:
                    EmailNotificationService.send_contact_request_notification(contact_req)
                except Exception as e:
                    # Log the error but don't prevent the request from being submitted
                    print(f"Error sending contact request notification: {str(e)}")

            messages.success(request, "Contact request sent successfully!")
            return redirect('services:dashboard')

        except ApplicantProfile.DoesNotExist:
            messages.error(request, "Applicant not found.")

    # Get list of applicants
    applicants = ApplicantProfile.objects.all()

    return render(request, 'services/contact_request.html', {
        'applicants': applicants
    })


@login_required
def payment_process(request):
    """View for processing payments using Stripe."""
    if not hasattr(request.user, 'hiring_partner_profile'):
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')

    hiring_partner_profile = request.user.hiring_partner_profile

    if request.method == 'POST':
        # Get the plan from the form
        plan = request.POST.get('plan', 'basic')
        stripe_token = request.POST.get('stripeToken')

        if not stripe_token:
            messages.error(request, "Payment processing failed. Please try again.")
            return redirect('services:payment')

        try:
            # Create or get Stripe customer
            customer = None
            if hiring_partner_profile.stripe_customer_id:
                try:
                    # Try to retrieve existing customer
                    customer = stripe.Customer.retrieve(hiring_partner_profile.stripe_customer_id)
                except stripe.error.StripeError:
                    # If retrieval fails, customer will remain None and a new one will be created
                    pass

            if not customer:
                # Create a new customer
                customer = StripeService.create_customer(request.user, stripe_token)
                # Save the customer ID to the profile
                hiring_partner_profile.stripe_customer_id = customer.id
                hiring_partner_profile.save(update_fields=['stripe_customer_id'])

            # Create a subscription
            subscription = StripeService.create_subscription(
                customer_id=customer.id,
                plan=plan
            )

            # Set subscription start and end dates
            from datetime import date, timedelta
            start_date = date.today()
            # End date is calculated based on the billing cycle (default: 30 days)
            end_date = start_date + timedelta(days=30)

            # Create the subscription in our database
            db_subscription = Subscription.objects.create(
                hiring_partner=hiring_partner_profile,
                plan=plan,
                start_date=start_date,
                end_date=end_date,
                status='active',
                is_auto_renew=request.POST.get('auto_renew', 'on') == 'on',
                payment_method='Credit Card',
                stripe_customer_id=customer.id,
                stripe_subscription_id=subscription.id
            )

            # Create a payment record
            Payment.objects.create(
                subscription=db_subscription,
                amount=get_plan_price(plan) / 100,  # Convert from cents
                status='succeeded',
                transaction_id=f"sub_{subscription.id}",
                stripe_payment_intent_id=subscription.latest_invoice.payment_intent.id if hasattr(subscription, 'latest_invoice') and subscription.latest_invoice.payment_intent else None
            )

            messages.success(request, f"Your {plan.title()} plan subscription has been activated successfully.")
            return redirect('services:subscription')

        except stripe.error.CardError as e:
            # Since it's a decline, stripe.error.CardError will be caught
            body = e.json_body
            err = body.get('error', {})
            messages.error(request, f"Payment failed: {err.get('message')}")
            return redirect('services:payment')
        except stripe.error.RateLimitError:
            # Too many requests made to the API too quickly
            messages.error(request, "Our payment system is experiencing high traffic. Please try again later.")
            return redirect('services:payment')
        except stripe.error.InvalidRequestError:
            # Invalid parameters were supplied to Stripe's API
            messages.error(request, "Invalid payment information. Please check your details and try again.")
            return redirect('services:payment')
        except stripe.error.AuthenticationError:
            # Authentication with Stripe's API failed
            messages.error(request, "Payment system authentication failed. Please contact support.")
            return redirect('services:payment')
        except stripe.error.APIConnectionError:
            # Network communication with Stripe failed
            messages.error(request, "Network error. Please check your connection and try again.")
            return redirect('services:payment')
        except stripe.error.StripeError:
            # Generic error
            messages.error(request, "Payment processing failed. Please try again later.")
            return redirect('services:payment')
        except Exception as e:
            # Something else happened, completely unrelated to Stripe
            messages.error(request, f"An unexpected error occurred: {str(e)}")
            return redirect('services:payment')

    # If GET request, show the payment form
    plan = request.GET.get('plan', 'basic')
    price = get_plan_price(plan)
    price_display = f"{price/100:.2f}"
    plan_name = plan.title()

    from datetime import datetime
    current_year = datetime.now().year

    return render(request, 'services/payment.html', {
        'plan': plan,
        'price': price,
        'price_display': price_display,
        'plan_name': plan_name,
        'current_year': current_year,
        'selected_plan': plan,
        'stripe_public_key': settings.STRIPE_PUBLISHABLE_KEY
    })


@login_required
def payment_success(request):
    """View for successful payments."""
    return render(request, 'services/payment_success.html')


@login_required
def payment_cancel(request):
    """View for cancelled payments."""
    return render(request, 'services/payment_cancel.html')


@login_required
def cancel_subscription(request, subscription_id):
    """View for canceling a subscription."""
    if not hasattr(request.user, 'hiring_partner_profile'):
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')

    hiring_partner_profile = request.user.hiring_partner_profile

    try:
        subscription = get_object_or_404(
            Subscription,
            id=subscription_id,
            hiring_partner=hiring_partner_profile
        )

        if subscription.status == 'active':
            # Cancel the subscription in Stripe if we have a Stripe subscription ID
            if subscription.stripe_subscription_id:
                try:
                    # Cancel the subscription at the end of the current billing period
                    StripeService.cancel_subscription(subscription.stripe_subscription_id)
                except stripe.error.StripeError as e:
                    messages.error(request, f"Error canceling subscription: {str(e)}")
                    return redirect('services:subscription')

            # Update our database
            subscription.status = 'cancelled'
            subscription.save()
            messages.success(request, "Your subscription has been cancelled successfully.")
        else:
            messages.warning(request, "This subscription is not active.")

    except Subscription.DoesNotExist:
        messages.error(request, "Subscription not found.")

    return redirect('services:subscription')


@login_required
def renew_subscription(request, subscription_id):
    """View for renewing a subscription."""
    if not hasattr(request.user, 'hiring_partner_profile'):
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')

    hiring_partner_profile = request.user.hiring_partner_profile

    try:
        subscription = get_object_or_404(
            Subscription,
            id=subscription_id,
            hiring_partner=hiring_partner_profile
        )

        if subscription.status in ['cancelled', 'expired']:
            # Redirect to payment page to create a new subscription
            return redirect(f"services:payment?plan={subscription.plan}")
        else:
            messages.warning(request, "This subscription is already active.")

    except Subscription.DoesNotExist:
        messages.error(request, "Subscription not found.")

    return redirect('services:subscription')


@login_required
def change_plan(request):
    """View for changing a subscription plan."""
    if not hasattr(request.user, 'hiring_partner_profile'):
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')

    hiring_partner_profile = request.user.hiring_partner_profile

    # Get current subscription
    current_subscription = Subscription.objects.filter(
        hiring_partner=hiring_partner_profile,
        status='active'
    ).first()

    if not current_subscription:
        messages.warning(request, "You don't have an active subscription to change.")
        return redirect('services:subscription')

    if request.method == 'POST':
        new_plan = request.POST.get('plan')
        if new_plan in ['basic', 'standard', 'premium', 'enterprise']:
            try:
                # Update the subscription in Stripe if we have a Stripe subscription ID
                if current_subscription.stripe_subscription_id:
                    # Update the subscription in Stripe
                    StripeService.update_subscription(
                        current_subscription.stripe_subscription_id,
                        new_plan
                    )

                # Update the subscription in our database
                current_subscription.plan = new_plan
                current_subscription.save()

                messages.success(request, f"Your subscription has been updated to the {new_plan.title()} plan.")
                return redirect('services:subscription')
            except stripe.error.StripeError as e:
                messages.error(request, f"Error updating subscription: {str(e)}")
                return redirect('services:change_plan')
        else:
            messages.error(request, "Invalid plan selected.")

    return render(request, 'services/change_plan.html', {
        'current_subscription': current_subscription
    })


@csrf_exempt
def stripe_webhook(request):
    """Handle Stripe webhooks."""
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

    if not sig_header:
        return HttpResponse(status=400)

    try:
        event = StripeService.handle_webhook_event(payload, sig_header)
    except ValueError:
        # Invalid payload
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError:
        # Invalid signature
        return HttpResponse(status=400)

    # Handle the checkout.session.completed event
    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']

        # Get the hiring partner profile
        hiring_partner_id = session.get('client_reference_id')
        plan = session.get('metadata', {}).get('plan')

        if hiring_partner_id and plan:
            try:
                hiring_partner = HiringPartnerProfile.objects.get(id=hiring_partner_id)

                # Create a subscription
                subscription = Subscription.objects.create(
                    hiring_partner=hiring_partner,
                    plan=plan,
                    start_date=timezone.now().date(),
                    end_date=timezone.now().date() + timezone.timedelta(days=30),
                    status='active',
                    stripe_customer_id=session.get('customer'),
                    stripe_subscription_id=session.get('subscription')
                )

                # Create a payment record
                Payment.objects.create(
                    subscription=subscription,
                    amount=get_plan_price(plan) / 100,  # Convert from cents
                    status='succeeded',
                    transaction_id=session.get('payment_intent'),
                    stripe_payment_intent_id=session.get('payment_intent')
                )

            except HiringPartnerProfile.DoesNotExist:
                return HttpResponse(status=404)

    # Handle subscription updated event
    elif event['type'] == 'customer.subscription.updated':
        subscription_data = event['data']['object']
        stripe_subscription_id = subscription_data.get('id')

        if stripe_subscription_id:
            try:
                subscription = Subscription.objects.get(stripe_subscription_id=stripe_subscription_id)

                # Update subscription status
                status = subscription_data.get('status')
                if status == 'active':
                    subscription.status = 'active'
                elif status == 'canceled':
                    subscription.status = 'cancelled'
                elif status == 'past_due':
                    subscription.status = 'pending'

                # Update subscription end date
                if subscription_data.get('current_period_end'):
                    end_timestamp = subscription_data.get('current_period_end')
                    end_date = timezone.datetime.fromtimestamp(end_timestamp).date()
                    subscription.end_date = end_date

                subscription.save()
            except Subscription.DoesNotExist:
                # Subscription not found in our database
                pass

    # Handle subscription deleted event
    elif event['type'] == 'customer.subscription.deleted':
        subscription_data = event['data']['object']
        stripe_subscription_id = subscription_data.get('id')

        if stripe_subscription_id:
            try:
                subscription = Subscription.objects.get(stripe_subscription_id=stripe_subscription_id)
                subscription.status = 'cancelled'
                subscription.save()
            except Subscription.DoesNotExist:
                # Subscription not found in our database
                pass

    # Handle payment succeeded event
    elif event['type'] == 'invoice.payment_succeeded':
        invoice_data = event['data']['object']
        stripe_subscription_id = invoice_data.get('subscription')

        if stripe_subscription_id:
            try:
                subscription = Subscription.objects.get(stripe_subscription_id=stripe_subscription_id)

                # Create a payment record
                Payment.objects.create(
                    subscription=subscription,
                    amount=invoice_data.get('amount_paid') / 100,  # Convert from cents
                    status='succeeded',
                    transaction_id=invoice_data.get('id'),
                    stripe_payment_intent_id=invoice_data.get('payment_intent'),
                    stripe_charge_id=invoice_data.get('charge')
                )

                # Update subscription status
                subscription.status = 'active'

                # Update subscription end date
                if invoice_data.get('lines', {}).get('data'):
                    for line in invoice_data.get('lines', {}).get('data', []):
                        if line.get('period', {}).get('end'):
                            end_timestamp = line.get('period', {}).get('end')
                            end_date = timezone.datetime.fromtimestamp(end_timestamp).date()
                            subscription.end_date = end_date
                            break

                subscription.save()
            except Subscription.DoesNotExist:
                # Subscription not found in our database
                pass

    # Handle payment failed event
    elif event['type'] == 'invoice.payment_failed':
        invoice_data = event['data']['object']
        stripe_subscription_id = invoice_data.get('subscription')

        if stripe_subscription_id:
            try:
                subscription = Subscription.objects.get(stripe_subscription_id=stripe_subscription_id)

                # Create a payment record
                Payment.objects.create(
                    subscription=subscription,
                    amount=invoice_data.get('amount_due') / 100,  # Convert from cents
                    status='failed',
                    transaction_id=invoice_data.get('id'),
                    stripe_payment_intent_id=invoice_data.get('payment_intent')
                )

                # Update subscription status if it's the final attempt
                if invoice_data.get('next_payment_attempt') is None:
                    subscription.status = 'expired'
                    subscription.save()
            except Subscription.DoesNotExist:
                # Subscription not found in our database
                pass

    return HttpResponse(status=200)


def get_plan_price(plan):
    """Helper function to get the price for a plan."""
    prices = {
        'basic': 9900,  # $99.00
        'standard': 19900,  # $199.00
        'premium': 29900,  # $299.00
        'enterprise': 49900  # $499.00
    }
    return prices.get(plan.lower(), 9900)


def calculate_profile_completion(profile):
    """
    Calculate the profile completion percentage for a hiring partner profile.

    This function checks all fields in the profile and calculates what percentage
    of them have been filled out.
    """
    # Define fields to check (excluding system fields like id, created_at, etc.)
    fields_to_check = [
        'company_name', 'company_size', 'profile_image', 'primary_location',
        'other_locations', 'branch', 'industry', 'company_description',
        'company_needs', 'average_wages', 'contract_types', 'working_hours',
        'work_location', 'phone_number', 'company_address', 'company_city',
        'company_state', 'company_zip', 'company_website', 'job_title',
        'services_offered'
    ]

    # Count completed fields
    completed = 0
    for field in fields_to_check:
        value = getattr(profile, field, None)
        if value not in [None, '', [], {}]:
            completed += 1

    # Calculate percentage
    total_fields = len(fields_to_check)
    completion_percentage = int((completed / total_fields) * 100)

    return completion_percentage


@login_required
def service_profile(request):
    """View for hiring partner profile and profile updates."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile

        if request.method == 'POST':
            form = HiringPartnerForm(request.POST, request.FILES, instance=hiring_partner_profile)
            if form.is_valid():
                form.save()
                messages.success(request, "Your profile has been updated successfully.")
                return redirect('services:profile')
        else:
            form = HiringPartnerForm(instance=hiring_partner_profile)

        # Calculate profile completion percentage
        completion_percentage = calculate_profile_completion(hiring_partner_profile)

        return render(request, 'services/profile.html', {
            'profile': hiring_partner_profile,
            'form': form,
            'completion_percentage': completion_percentage
        })
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


@login_required
def service_browser(request):
    """View for browsing hiring partners."""
    hiring_partner_profiles = HiringPartnerProfile.objects.all()
    return render(request, 'services/browser.html', {'profiles': hiring_partner_profiles})


@login_required
def service_account(request):
    """View for hiring partner account settings."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile
        return render(request, 'services/account.html', {'profile': hiring_partner_profile})
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


@login_required
def contact_request_list(request):
    """View for listing all contact requests."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile
        contact_requests = ContactRequest.objects.filter(sender=hiring_partner_profile)

        # Paginate the results
        paginator = Paginator(contact_requests, 10)  # Show 10 requests per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        return render(request, 'services/contact_requests.html', {
            'page_obj': page_obj,
            'total_requests': contact_requests.count(),
        })
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


@login_required
def contact_request_detail(request, request_id):
    """View for viewing a specific contact request."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile
        contact_req = get_object_or_404(ContactRequest, id=request_id, sender=hiring_partner_profile)

        return render(request, 'services/contact_request_detail.html', {
            'contact_request': contact_req
        })
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


@login_required
def offer_list(request):
    """View for listing all offers."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile
        offers = Offer.objects.filter(hiring_partner=hiring_partner_profile)

        # Paginate the results
        paginator = Paginator(offers, 10)  # Show 10 offers per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        return render(request, 'services/offers.html', {
            'page_obj': page_obj,
            'total_offers': offers.count(),
        })
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


@login_required
def create_offer(request):
    """View for creating a new offer."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile

        if request.method == 'POST':
            form = OfferForm(request.POST)
            if form.is_valid():
                offer = form.save(commit=False)
                offer.hiring_partner = hiring_partner_profile
                offer.save()
                messages.success(request, "Your offer has been created successfully.")
                return redirect('services:offers')
        else:
            form = OfferForm()

        return render(request, 'services/create_offer.html', {'form': form})
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


@login_required
def edit_offer(request, offer_id):
    """View for editing an existing offer."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile
        offer = get_object_or_404(Offer, id=offer_id, hiring_partner=hiring_partner_profile)

        if request.method == 'POST':
            form = OfferForm(request.POST, instance=offer)
            if form.is_valid():
                form.save()
                messages.success(request, "Your offer has been updated successfully.")
                return redirect('services:offers')
        else:
            form = OfferForm(instance=offer)

        return render(request, 'services/edit_offer.html', {
            'form': form,
            'offer': offer
        })
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


@login_required
def applicant_list(request):
    """View for browsing applicants."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile

        # Check if the hiring partner has an active subscription
        has_subscription = Subscription.objects.filter(
            hiring_partner=hiring_partner_profile,
            status='active',
            end_date__gte=timezone.now().date()
        ).exists()

        if not has_subscription:
            messages.warning(request, "You need an active subscription to browse applicants.")
            return redirect('services:subscription')

        # Get filter parameters
        industry = request.GET.get('industry')
        role = request.GET.get('role')
        experience = request.GET.get('experience')
        search_query = request.GET.get('q')

        # Start with all applicants
        applicants = ApplicantProfile.objects.all()

        # Apply filters
        if industry:
            applicants = applicants.filter(industry=industry)
        if role:
            applicants = applicants.filter(role=role)
        if experience:
            if experience == 'less_than_1':
                applicants = applicants.filter(position_experience_years__lt=1)
            elif experience == '1_3':
                applicants = applicants.filter(position_experience_years__gte=1, position_experience_years__lte=3)
            elif experience == '3_5':
                applicants = applicants.filter(position_experience_years__gte=3, position_experience_years__lte=5)
            elif experience == '5_plus':
                applicants = applicants.filter(position_experience_years__gt=5)

        # Apply search query
        if search_query:
            applicants = applicants.filter(
                Q(user__first_name__icontains=search_query) |
                Q(user__last_name__icontains=search_query) |
                Q(industry__icontains=search_query) |
                Q(role__icontains=search_query) |
                Q(bio__icontains=search_query)
            )

        # Get unique industries and roles for filter dropdowns
        industries = ApplicantProfile.objects.values_list('industry', flat=True).distinct()
        roles = ApplicantProfile.objects.values_list('role', flat=True).distinct()

        # Paginate the results
        paginator = Paginator(applicants, 12)  # Show 12 applicants per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        return render(request, 'services/applicants.html', {
            'page_obj': page_obj,
            'total_applicants': applicants.count(),
            'industries': industries,
            'roles': roles,
            'selected_industry': industry,
            'selected_role': role,
            'selected_experience': experience,
            'search_query': search_query,
        })
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


@login_required
def applicant_detail(request, applicant_id):
    """View for viewing a specific applicant's details."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile

        # Check if the hiring partner has an active subscription
        has_subscription = Subscription.objects.filter(
            hiring_partner=hiring_partner_profile,
            status='active',
            end_date__gte=timezone.now().date()
        ).exists()

        if not has_subscription:
            messages.warning(request, "You need an active subscription to view applicant details.")
            return redirect('services:subscription')

        applicant = get_object_or_404(ApplicantProfile, id=applicant_id)

        # Check if there's a contact request already sent to this applicant
        existing_request = ContactRequest.objects.filter(
            sender=hiring_partner_profile,
            recipient=applicant
        ).first()

        # Handle contact request form submission
        if request.method == 'POST':
            form = ContactRequestForm(request.POST)
            if form.is_valid():
                if existing_request:
                    messages.info(request, "You have already sent a contact request to this applicant.")
                else:
                    contact_req = form.save(commit=False)
                    contact_req.sender = hiring_partner_profile
                    contact_req.recipient = applicant
                    contact_req.save()

                    # Send email notification if the service is available
                    if email_service_available:
                        try:
                            EmailNotificationService.send_contact_request_notification(contact_req)
                        except Exception as e:
                            # Log the error but don't prevent the request from being submitted
                            print(f"Error sending contact request notification: {str(e)}")

                    messages.success(request, "Your contact request has been sent successfully.")
                return redirect('services:applicant_detail', applicant_id=applicant_id)
        else:
            form = ContactRequestForm()

        # Get refined info if available
        try:
            refined_info = RefinedInfo.objects.filter(
                application__applicant=applicant
            ).order_by('-created_at').first()
        except RefinedInfo.DoesNotExist:
            refined_info = None

        return render(request, 'services/applicant_detail.html', {
            'applicant': applicant,
            'refined_info': refined_info,
            'form': form,
            'existing_request': existing_request,
        })
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


@login_required
def faq(request):
    """View for FAQ page."""
    return render(request, 'services/faq.html')


@login_required
def support(request):
    """View for support page."""
    return render(request, 'services/support.html')


@login_required
def contact_request_cancel(request, request_id):
    """View for canceling a contact request."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile
        contact_req = get_object_or_404(ContactRequest, id=request_id, sender=hiring_partner_profile)

        # Only allow canceling pending requests
        if contact_req.status == 'pending':
            # Delete the request
            contact_req.delete()
            messages.success(request, "Contact request canceled successfully.")
        else:
            messages.error(request, "Cannot cancel a request that has already been responded to.")

        return redirect('services:contact_requests')
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


@login_required
def delete_offer(request, offer_id):
    """View for deleting an offer."""
    try:
        hiring_partner_profile = request.user.hiring_partner_profile
        offer = get_object_or_404(Offer, id=offer_id, hiring_partner=hiring_partner_profile)

        if request.method == 'POST':
            offer.delete()
            messages.success(request, "Offer deleted successfully.")

        return redirect('services:offers')
    except HiringPartnerProfile.DoesNotExist:
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')


# Communication API Endpoints

@login_required
def save_communication(request):
    """AJAX endpoint for saving communication logs."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Invalid method'})

    if not hasattr(request.user, 'hiring_partner_profile'):
        return JsonResponse({'success': False, 'error': 'No hiring partner profile'})

    try:
        candidate_id = request.POST.get('candidate_id')
        communication_type_name = request.POST.get('communication_type')
        subject = request.POST.get('subject', '')
        content = request.POST.get('content')
        requires_follow_up = request.POST.get('requires_follow_up') == 'on'
        follow_up_date = request.POST.get('follow_up_date')

        if not candidate_id or not communication_type_name or not content:
            return JsonResponse({'success': False, 'error': 'Missing required fields'})

        hiring_partner_profile = request.user.hiring_partner_profile
        candidate = get_object_or_404(ApplicantProfile, id=candidate_id)

        # Get or create communication type
        communication_type, created = CommunicationType.objects.get_or_create(
            name=communication_type_name,
            defaults={
                'display_name': communication_type_name.replace('_', ' ').title(),
                'icon': get_communication_icon(communication_type_name),
                'color': get_communication_color(communication_type_name)
            }
        )

        # Parse follow-up date if provided
        follow_up_datetime = None
        if requires_follow_up and follow_up_date:
            try:
                from datetime import datetime
                follow_up_datetime = datetime.strptime(follow_up_date, '%Y-%m-%dT%H:%M')
            except ValueError:
                pass

        # Create communication log
        communication = CommunicationLog.objects.create(
            candidate=candidate,
            hiring_partner=hiring_partner_profile,
            communication_type=communication_type,
            subject=subject,
            content=content,
            requires_follow_up=requires_follow_up,
            follow_up_date=follow_up_datetime,
            completed_at=timezone.now()
        )

        # Create follow-up reminder if needed
        if requires_follow_up and follow_up_datetime:
            FollowUpReminder.objects.create(
                communication=communication,
                hiring_partner=hiring_partner_profile,
                candidate=candidate,
                title=f"Follow up: {subject or communication_type.display_name}",
                description=f"Follow up on {communication_type.display_name} with {candidate.user.get_full_name()}",
                scheduled_for=follow_up_datetime
            )

        return JsonResponse({
            'success': True,
            'message': 'Communication saved successfully',
            'communication_id': communication.id
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def get_communication_history(request, candidate_id):
    """AJAX endpoint for getting communication history for a candidate."""
    if not hasattr(request.user, 'hiring_partner_profile'):
        return JsonResponse({'success': False, 'error': 'No hiring partner profile'})

    try:
        hiring_partner_profile = request.user.hiring_partner_profile
        candidate = get_object_or_404(ApplicantProfile, id=candidate_id)

        # Get communications for this candidate and hiring partner
        communications = CommunicationLog.objects.filter(
            candidate=candidate,
            hiring_partner=hiring_partner_profile
        ).select_related('communication_type').order_by('-created_at')

        # Format communications for JSON response
        communication_data = []
        for comm in communications:
            communication_data.append({
                'id': comm.id,
                'type': comm.communication_type.display_name,
                'type_icon': comm.communication_type.icon,
                'type_color': comm.communication_type.color,
                'subject': comm.subject,
                'content': comm.content,
                'created_at': comm.created_at.strftime('%Y-%m-%d %H:%M'),
                'requires_follow_up': comm.requires_follow_up,
                'follow_up_date': comm.follow_up_date.strftime('%Y-%m-%d %H:%M') if comm.follow_up_date else None,
                'follow_up_completed': comm.follow_up_completed
            })

        return JsonResponse({
            'success': True,
            'communications': communication_data
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def update_follow_up(request, communication_id):
    """AJAX endpoint for marking follow-ups as completed."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Invalid method'})

    if not hasattr(request.user, 'hiring_partner_profile'):
        return JsonResponse({'success': False, 'error': 'No hiring partner profile'})

    try:
        hiring_partner_profile = request.user.hiring_partner_profile
        communication = get_object_or_404(
            CommunicationLog,
            id=communication_id,
            hiring_partner=hiring_partner_profile
        )

        # Mark follow-up as completed
        communication.follow_up_completed = True
        communication.save()

        # Update any related follow-up reminders
        FollowUpReminder.objects.filter(
            communication=communication
        ).update(status='completed', completed_at=timezone.now())

        return JsonResponse({
            'success': True,
            'message': 'Follow-up marked as completed'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


def get_communication_icon(communication_type):
    """Helper function to get icon for communication type."""
    icons = {
        'email': 'bi-envelope',
        'phone': 'bi-telephone',
        'meeting': 'bi-calendar-event',
        'interview': 'bi-camera-video',
        'message': 'bi-chat-dots',
        'note': 'bi-sticky'
    }
    return icons.get(communication_type, 'bi-chat')


def get_communication_color(communication_type):
    """Helper function to get color for communication type."""
    colors = {
        'email': '#0d6efd',
        'phone': '#198754',
        'meeting': '#fd7e14',
        'interview': '#dc3545',
        'message': '#6f42c1',
        'note': '#6c757d'
    }
    return colors.get(communication_type, '#6c757d')


# Analytics Dashboard

@login_required
def analytics_dashboard(request):
    """Analytics dashboard for recruitment metrics."""
    if not hasattr(request.user, 'hiring_partner_profile'):
        messages.error(request, "You need to create a hiring partner profile first.")
        return redirect('services:register')

    hiring_partner_profile = request.user.hiring_partner_profile

    # Get pipeline candidates for this hiring partner
    pipeline_candidates = CandidatePipeline.objects.filter(
        hiring_partner=hiring_partner_profile
    ).select_related('candidate', 'current_stage')

    # Calculate funnel metrics
    stages = PipelineStage.objects.filter(is_active=True).order_by('order')
    funnel_data = []
    total_candidates = pipeline_candidates.count()

    for stage in stages:
        stage_count = pipeline_candidates.filter(current_stage=stage).count()
        percentage = (stage_count / total_candidates * 100) if total_candidates > 0 else 0
        funnel_data.append({
            'stage': stage.display_name,
            'count': stage_count,
            'percentage': round(percentage, 1)
        })

    # Calculate time-to-hire metrics
    hired_candidates = pipeline_candidates.filter(current_stage__name='hired')
    avg_time_to_hire = 0
    if hired_candidates.exists():
        total_days = 0
        count = 0
        for candidate in hired_candidates:
            # Get the first stage entry for this candidate
            first_entry = CandidateStageHistory.objects.filter(
                candidate=candidate.candidate,
                hiring_partner=hiring_partner_profile
            ).order_by('changed_at').first()

            if first_entry:
                days_diff = (timezone.now().date() - first_entry.changed_at.date()).days
                total_days += days_diff
                count += 1

        if count > 0:
            avg_time_to_hire = round(total_days / count, 1)

    # Communication metrics
    communications = CommunicationLog.objects.filter(
        hiring_partner=hiring_partner_profile
    )
    total_communications = communications.count()
    pending_follow_ups = communications.filter(
        requires_follow_up=True,
        follow_up_completed=False
    ).count()

    # Recent activity
    recent_stage_changes = CandidateStageHistory.objects.filter(
        hiring_partner=hiring_partner_profile
    ).select_related('candidate', 'stage').order_by('-changed_at')[:10]

    # Source attribution (simplified - based on how candidates were added)
    source_data = [
        {'source': 'Direct Applications', 'count': total_candidates, 'percentage': 100}
    ]

    context = {
        'hiring_partner': hiring_partner_profile,
        'total_candidates': total_candidates,
        'funnel_data': funnel_data,
        'avg_time_to_hire': avg_time_to_hire,
        'total_communications': total_communications,
        'pending_follow_ups': pending_follow_ups,
        'recent_stage_changes': recent_stage_changes,
        'source_data': source_data,
        'hired_count': hired_candidates.count(),
    }

    return render(request, 'services/analytics_dashboard.html', context)
