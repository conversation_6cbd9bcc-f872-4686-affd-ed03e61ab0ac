{% extends 'base.html' %}
{% load i18n %}

{% block title %}Analytics Dashboard - Smarch{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        transition: transform 0.2s;
    }
    .metric-card:hover {
        transform: translateY(-2px);
    }
    .funnel-bar {
        height: 30px;
        background: linear-gradient(90deg, #0d6efd, #6610f2);
        border-radius: 15px;
        position: relative;
        overflow: hidden;
    }
    .funnel-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2));
    }
    .activity-item {
        border-left: 3px solid #0d6efd;
        padding-left: 15px;
        margin-bottom: 15px;
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">{% trans "Analytics Dashboard" %}</h1>
                    <p class="text-muted">{% trans "Recruitment metrics and insights" %}</p>
                </div>
                <div>
                    <a href="{% url 'services:pipeline' %}" class="btn btn-outline-primary">
                        <i class="bi bi-kanban"></i> {% trans "Back to Pipeline" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics Row -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card metric-card h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-primary mb-2">{{ total_candidates }}</div>
                    <h6 class="card-title text-muted">{% trans "Total Candidates" %}</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-success mb-2">{{ hired_count }}</div>
                    <h6 class="card-title text-muted">{% trans "Hired" %}</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-info mb-2">{{ avg_time_to_hire }}</div>
                    <h6 class="card-title text-muted">{% trans "Avg. Days to Hire" %}</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-warning mb-2">{{ pending_follow_ups }}</div>
                    <h6 class="card-title text-muted">{% trans "Pending Follow-ups" %}</h6>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Recruitment Funnel -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-funnel"></i> {% trans "Recruitment Funnel" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% for stage_data in funnel_data %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">{{ stage_data.stage }}</span>
                            <span class="badge bg-primary">{{ stage_data.count }} ({{ stage_data.percentage }}%)</span>
                        </div>
                        <div class="funnel-bar" style="width: {{ stage_data.percentage }}%;">
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">{% trans "No pipeline data available" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Communication Stats -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-chat-dots"></i> {% trans "Communication Stats" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="h4 text-primary">{{ total_communications }}</div>
                            <small class="text-muted">{% trans "Total Communications" %}</small>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 text-warning">{{ pending_follow_ups }}</div>
                            <small class="text-muted">{% trans "Pending Follow-ups" %}</small>
                        </div>
                    </div>
                    {% if pending_follow_ups > 0 %}
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        {% trans "You have pending follow-ups that need attention" %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-activity"></i> {% trans "Recent Activity" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% for activity in recent_stage_changes %}
                    <div class="activity-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>{{ activity.candidate.user.get_full_name|default:activity.candidate.user.username }}</strong>
                                {% trans "moved to" %} <span class="badge bg-info">{{ activity.stage.display_name }}</span>
                                {% if activity.notes %}
                                <br><small class="text-muted">{{ activity.notes }}</small>
                                {% endif %}
                            </div>
                            <small class="text-muted">{{ activity.created_at|timesince }} {% trans "ago" %}</small>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">{% trans "No recent activity" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning"></i> {% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'services:pipeline' %}" class="btn btn-primary">
                            <i class="bi bi-kanban"></i> {% trans "Manage Pipeline" %}
                        </a>
                        <a href="{% url 'services:applicants' %}" class="btn btn-outline-primary">
                            <i class="bi bi-people"></i> {% trans "Browse Candidates" %}
                        </a>
                        <a href="{% url 'services:contact_requests' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-envelope"></i> {% trans "Contact Requests" %}
                        </a>
                    </div>
                    
                    {% if pending_follow_ups > 0 %}
                    <div class="mt-3">
                        <div class="alert alert-info">
                            <small>
                                <i class="bi bi-info-circle"></i>
                                {% trans "Check your pipeline for pending follow-ups" %}
                            </small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive features here
    console.log('Analytics dashboard loaded');
});
</script>
{% endblock %}
