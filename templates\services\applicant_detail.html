{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Applicant Profile - Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row mb-4">
    <div class="col-md-8">
      <h1 class="mb-3">Applicant Profile</h1>
    </div>
    <div class="col-md-4 text-md-end">
      <a href="{% url 'services:applicants' %}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> Back to Applicants
      </a>
    </div>
  </div>

  <div class="row">
    <!-- Left Column - Profile Information -->
    <div class="col-lg-8">
      <div class="card mb-4">
        <div class="card-body">
          <div class="d-flex align-items-center mb-4">
            {% if applicant.image %}
              <img src="{{ applicant.image.url }}" alt="{{ applicant.user.get_full_name }}" class="rounded-circle me-4" style="width: 100px; height: 100px; object-fit: cover;">
            {% else %}
              <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-4" style="width: 100px; height: 100px;">
                <i class="bi bi-person text-secondary" style="font-size: 2.5rem;"></i>
              </div>
            {% endif %}
            <div>
              <h3 class="mb-1">{{ applicant.user.get_full_name }}</h3>
              <p class="text-muted mb-2">{{ applicant.role|default:"Role not specified" }}</p>
              <p class="mb-0">
                <i class="bi bi-geo-alt me-1"></i> {{ applicant.location|default:"Location not specified" }}
              </p>
            </div>
          </div>

          <h5 class="border-bottom pb-2 mb-3">About</h5>
          <p>{{ applicant.bio|default:"No bio provided." }}</p>

          <h5 class="border-bottom pb-2 mb-3 mt-4">Professional Information</h5>
          <div class="row">
            <div class="col-md-6 mb-3">
              <p class="mb-1"><strong>Industry:</strong></p>
              <p>{{ applicant.industry|default:"Not specified" }}</p>
            </div>
            <div class="col-md-6 mb-3">
              <p class="mb-1"><strong>Branch:</strong></p>
              <p>{{ applicant.branch|default:"Not specified" }}</p>
            </div>
            <div class="col-md-6 mb-3">
              <p class="mb-1"><strong>Experience:</strong></p>
              <p>{{ applicant.position_experience_years }} years</p>
            </div>
            <div class="col-md-6 mb-3">
              <p class="mb-1"><strong>Work Permit:</strong></p>
              <p>{% if applicant.work_permit %}Yes{% else %}No{% endif %}</p>
            </div>
          </div>

          {% if refined_info %}
            <h5 class="border-bottom pb-2 mb-3 mt-4">Skills & Languages</h5>
            <div class="row">
              <div class="col-md-6 mb-3">
                <p class="mb-1"><strong>Skills:</strong></p>
                <p>
                  {% for skill in refined_info.skills %}
                    <span class="badge bg-primary me-1">{{ skill }}</span>
                  {% empty %}
                    Not specified
                  {% endfor %}
                </p>
              </div>
              <div class="col-md-6 mb-3">
                <p class="mb-1"><strong>Languages:</strong></p>
                <p>
                  {% for language in refined_info.languages %}
                    <span class="badge bg-info text-dark me-1">{{ language }}</span>
                  {% empty %}
                    Not specified
                  {% endfor %}
                </p>
              </div>
            </div>

            <h5 class="border-bottom pb-2 mb-3 mt-4">Work Preferences</h5>
            <div class="row">
              <div class="col-md-12 mb-3">
                <p class="mb-1"><strong>Preferred Work Types:</strong></p>
                <p>
                  {% for pref in refined_info.work_preferences %}
                    <span class="badge bg-light text-dark me-1">
                      {% if pref == 'full_time' %}Full time
                      {% elif pref == 'part_time' %}Part time
                      {% elif pref == 'freelance' %}Freelance
                      {% else %}{{ pref }}
                      {% endif %}
                    </span>
                  {% empty %}
                    Not specified
                  {% endfor %}
                </p>
              </div>
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Right Column - Contact Form -->
    <div class="col-lg-4">
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Contact This Applicant</h5>
        </div>
        <div class="card-body">
          {% if existing_request %}
            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i> You have already sent a contact request to this applicant.
            </div>
            <div class="card mb-3">
              <div class="card-body">
                <h6 class="card-subtitle mb-2 text-muted">Your Message:</h6>
                <p>{{ existing_request.message }}</p>
                <p class="text-muted mb-0 small">Sent on {{ existing_request.created_at|date:"F d, Y" }}</p>
              </div>
              <div class="card-footer bg-light">
                <p class="mb-0">
                  <strong>Status:</strong>
                  {% if existing_request.status == 'pending' %}
                    <span class="badge bg-warning text-dark">Pending</span>
                  {% elif existing_request.status == 'accepted' %}
                    <span class="badge bg-success">Accepted</span>
                  {% elif existing_request.status == 'rejected' %}
                    <span class="badge bg-danger">Rejected</span>
                  {% endif %}
                </p>
              </div>
            </div>
          {% else %}
            <form method="post">
              {% csrf_token %}
              {{ form|crispy }}
              <div class="d-grid mt-3">
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-send"></i> Send Contact Request
                </button>
              </div>
            </form>
          {% endif %}
        </div>
      </div>

      <div class="card">
        <div class="card-header bg-light">
          <h5 class="mb-0">Applicant Details</h5>
        </div>
        <div class="card-body">
          <p class="mb-2">
            <strong><i class="bi bi-person-badge me-2"></i>Case Number:</strong><br>
            {{ applicant.case_number }}
          </p>
          <p class="mb-2">
            <strong><i class="bi bi-sort-numeric-down me-2"></i>Queue Position:</strong><br>
            {{ applicant.get_display_queue_position }}
          </p>
          <p class="mb-0">
            <strong><i class="bi bi-calendar-check me-2"></i>Registered:</strong><br>
            {{ applicant.created_at|date:"F d, Y" }}
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
