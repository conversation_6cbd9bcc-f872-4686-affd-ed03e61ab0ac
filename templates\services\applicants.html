{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Browse Applicants - Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row mb-4">
    <div class="col-md-8">
      <h1 class="mb-3">Browse Applicants</h1>
      <p class="text-muted">Find the perfect candidate for your company. Use the filters to narrow down your search.</p>
    </div>
    <div class="col-md-4 text-md-end">
      <a href="{% url 'services:dashboard' %}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> Back to Dashboard
      </a>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="card mb-4">
    <div class="card-body">
      <form method="get" action="{% url 'services:applicants' %}" class="row g-3">
        <div class="col-md-4">
          <label for="q" class="form-label">Search</label>
          <input type="text" class="form-control" id="q" name="q" placeholder="Search by name, skills, etc." value="{{ search_query }}">
        </div>
        <div class="col-md-3">
          <label for="industry" class="form-label">Industry</label>
          <select class="form-select" id="industry" name="industry">
            <option value="">All Industries</option>
            {% for industry in industries %}
              <option value="{{ industry }}" {% if selected_industry == industry %}selected{% endif %}>{{ industry }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-3">
          <label for="role" class="form-label">Role</label>
          <select class="form-select" id="role" name="role">
            <option value="">All Roles</option>
            {% for role in roles %}
              <option value="{{ role }}" {% if selected_role == role %}selected{% endif %}>{{ role }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-2">
          <label for="experience" class="form-label">Experience</label>
          <select class="form-select" id="experience" name="experience">
            <option value="">Any</option>
            <option value="less_than_1" {% if selected_experience == 'less_than_1' %}selected{% endif %}>< 1 year</option>
            <option value="1_3" {% if selected_experience == '1_3' %}selected{% endif %}>1-3 years</option>
            <option value="3_5" {% if selected_experience == '3_5' %}selected{% endif %}>3-5 years</option>
            <option value="5_plus" {% if selected_experience == '5_plus' %}selected{% endif %}>5+ years</option>
          </select>
        </div>
        <div class="col-12 text-end">
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-search"></i> Search
          </button>
          <a href="{% url 'services:applicants' %}" class="btn btn-outline-secondary">
            <i class="bi bi-x-circle"></i> Clear Filters
          </a>
        </div>
      </form>
    </div>
  </div>

  <!-- Results -->
  <div class="row mb-3">
    <div class="col">
      <h5>{{ total_applicants }} Applicants Found</h5>
    </div>
  </div>

  {% if page_obj %}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4 mb-4">
      {% for applicant in page_obj %}
        <div class="col">
          <div class="card h-100">
            <div class="card-body">
              <div class="d-flex align-items-center mb-3">
                {% if applicant.image %}
                  <img src="{{ applicant.image.url }}" alt="{{ applicant.user.get_full_name }}" class="rounded-circle me-3" style="width: 60px; height: 60px; object-fit: cover;">
                {% else %}
                  <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-person text-secondary" style="font-size: 1.5rem;"></i>
                  </div>
                {% endif %}
                <div>
                  <h5 class="card-title mb-0">{{ applicant.user.get_full_name }}</h5>
                  <p class="text-muted mb-0">{{ applicant.role|default:"Role not specified" }}</p>
                </div>
              </div>
              
              <div class="mb-3">
                <p class="mb-1"><strong>Industry:</strong> {{ applicant.industry|default:"Not specified" }}</p>
                <p class="mb-1"><strong>Location:</strong> {{ applicant.location|default:"Not specified" }}</p>
                <p class="mb-1"><strong>Experience:</strong> {{ applicant.position_experience_years }} years</p>
              </div>
              
              <p class="card-text">{{ applicant.bio|truncatechars:100 }}</p>
              
              <div class="d-grid">
                <a href="{% url 'services:applicant_detail' applicant.id %}" class="btn btn-outline-primary">
                  View Profile
                </a>
              </div>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
      <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
          {% if page_obj.has_previous %}
            <li class="page-item">
              <a class="page-link" href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_industry %}&industry={{ selected_industry }}{% endif %}{% if selected_role %}&role={{ selected_role }}{% endif %}{% if selected_experience %}&experience={{ selected_experience }}{% endif %}" aria-label="First">
                <span aria-hidden="true">&laquo;&laquo;</span>
              </a>
            </li>
            <li class="page-item">
              <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_industry %}&industry={{ selected_industry }}{% endif %}{% if selected_role %}&role={{ selected_role }}{% endif %}{% if selected_experience %}&experience={{ selected_experience }}{% endif %}" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
          {% else %}
            <li class="page-item disabled">
              <a class="page-link" href="#" aria-label="First">
                <span aria-hidden="true">&laquo;&laquo;</span>
              </a>
            </li>
            <li class="page-item disabled">
              <a class="page-link" href="#" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
          {% endif %}
          
          {% for i in page_obj.paginator.page_range %}
            {% if page_obj.number == i %}
              <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
            {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
              <li class="page-item">
                <a class="page-link" href="?page={{ i }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_industry %}&industry={{ selected_industry }}{% endif %}{% if selected_role %}&role={{ selected_role }}{% endif %}{% if selected_experience %}&experience={{ selected_experience }}{% endif %}">{{ i }}</a>
              </li>
            {% endif %}
          {% endfor %}
          
          {% if page_obj.has_next %}
            <li class="page-item">
              <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_industry %}&industry={{ selected_industry }}{% endif %}{% if selected_role %}&role={{ selected_role }}{% endif %}{% if selected_experience %}&experience={{ selected_experience }}{% endif %}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
            <li class="page-item">
              <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_industry %}&industry={{ selected_industry }}{% endif %}{% if selected_role %}&role={{ selected_role }}{% endif %}{% if selected_experience %}&experience={{ selected_experience }}{% endif %}" aria-label="Last">
                <span aria-hidden="true">&raquo;&raquo;</span>
              </a>
            </li>
          {% else %}
            <li class="page-item disabled">
              <a class="page-link" href="#" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
            <li class="page-item disabled">
              <a class="page-link" href="#" aria-label="Last">
                <span aria-hidden="true">&raquo;&raquo;</span>
              </a>
            </li>
          {% endif %}
        </ul>
      </nav>
    {% endif %}
  {% else %}
    <div class="alert alert-info">
      <i class="bi bi-info-circle me-2"></i> No applicants found matching your criteria. Try adjusting your filters.
    </div>
  {% endif %}
</div>
{% endblock %}
