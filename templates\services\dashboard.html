{% extends "common/base.html" %}
{% load i18n %}
{% load static %}
{% load users_extras %}

{% block title %}{% trans "Hiring Partner Dashboard" %} | Smarch{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <!-- Sidebar -->
    <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse" id="sidebarMenu">
      <div class="position-sticky pt-3">
        <ul class="nav flex-column">
          <li class="nav-item">
            <a class="nav-link active" href="#">
              <i class="bi bi-speedometer2 me-2"></i>
              {% trans "Dashboard" %}
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{% url 'services:offers' %}">
              <i class="bi bi-briefcase me-2"></i>
              {% trans "My Offers" %}
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{% url 'services:contact_requests' %}">
              <i class="bi bi-envelope me-2"></i>
              {% trans "Contact Requests" %}
              {% if new_requests_count > 0 %}
                <span class="badge bg-danger ms-2">{{ new_requests_count }}</span>
              {% endif %}
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{% url 'messaging:inbox' %}">
              <i class="bi bi-chat-dots me-2"></i>
              {% trans "Messages" %}
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{% url 'services:pipeline' %}">
              <i class="bi bi-kanban me-2"></i>
              {% trans "Candidate Pipeline" %}
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{% url 'services:applicants' %}">
              <i class="bi bi-people me-2"></i>
              {% trans "Browse Applicants" %}
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{% url 'services:subscription' %}">
              <i class="bi bi-credit-card me-2"></i>
              {% trans "Subscription" %}
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{% url 'services:profile' %}">
              <i class="bi bi-person-circle me-2"></i>
              {% trans "Company Profile" %}
            </a>
          </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
          <span>{% trans "Help & Support" %}</span>
        </h6>
        <ul class="nav flex-column mb-2">
          <li class="nav-item">
            <a class="nav-link" href="{% url 'services:faq' %}">
              <i class="bi bi-question-circle me-2"></i>
              {% trans "FAQ" %}
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{% url 'services:support' %}">
              <i class="bi bi-headset me-2"></i>
              {% trans "Contact Support" %}
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
      <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">{% trans "Hiring Partner Dashboard" %}</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
          <div class="btn-group me-2">
            <a href="{% url 'services:pipeline' %}" class="btn btn-sm btn-primary">
              <i class="bi bi-kanban"></i> {% trans "Candidate Pipeline" %}
            </a>
            <a href="{% url 'services:create_offer' %}" class="btn btn-sm btn-outline-primary">
              <i class="bi bi-plus-lg"></i> {% trans "New Offer" %}
            </a>
          </div>
        </div>
      </div>

      <!-- Subscription Status -->
      <div class="card mb-4 border-0 shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">{% trans "Subscription Status" %}</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>{% trans "Current Plan" %}:
                <span class="fw-bold">
                  {% if subscription.plan_type == 'basic' %}
                    {% trans "Basic" %}
                  {% elif subscription.plan_type == 'standard' %}
                    {% trans "Standard" %}
                  {% elif subscription.plan_type == 'premium' %}
                    {% trans "Premium" %}
                  {% else %}
                    {% trans "None" %}
                  {% endif %}
                </span>
              </h6>
              <h6>{% trans "Status" %}:
                {% if subscription.is_active %}
                  <span class="badge bg-success">{% trans "Active" %}</span>
                {% else %}
                  <span class="badge bg-danger">{% trans "Inactive" %}</span>
                {% endif %}
              </h6>
              {% if subscription.is_active %}
                <p>{% trans "Next billing date" %}: {{ subscription.next_billing_date }}</p>
              {% endif %}
            </div>
            <div class="col-md-6">
              <h6>{% trans "Contact Requests This Month" %}:</h6>
              <div class="progress mb-2">
                {% if subscription.plan_type == 'premium' %}
                  <div class="progress-bar" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">{% trans "Unlimited" %}</div>
                {% elif subscription.plan_type == 'standard' %}
                  <div class="progress-bar bg-{% with diff=current_requests|sub:30|abs %}{% if diff|lt:10 %}success{% elif diff|lt:20 %}warning{% else %}danger{% endif %}{% endwith %}" role="progressbar" style="width: {{ current_requests|default:0|mul:100|div:30 }}%" aria-valuenow="{{ current_requests|default:0 }}" aria-valuemin="0" aria-valuemax="30">{{ current_requests|default:0 }}/30</div>
                {% elif subscription.plan_type == 'basic' %}
                  <div class="progress-bar bg-{% with diff=current_requests|sub:10|abs %}{% if diff|lt:4 %}success{% elif diff|lt:7 %}warning{% else %}danger{% endif %}{% endwith %}" role="progressbar" style="width: {{ current_requests|default:0|mul:100|div:10 }}%" aria-valuenow="{{ current_requests|default:0 }}" aria-valuemin="0" aria-valuemax="10">{{ current_requests|default:0 }}/10</div>
                {% endif %}
              </div>
              {% if not subscription.is_active %}
                <div class="d-grid gap-2 mt-3">
                  <a href="{% url 'services:subscription' %}" class="btn btn-primary">{% trans "Subscribe Now" %}</a>
                </div>
              {% elif subscription.plan_type != 'premium' %}
                <div class="d-grid gap-2 mt-3">
                  <a href="{% url 'services:subscription' %}" class="btn btn-outline-primary btn-sm">{% trans "Upgrade Plan" %}</a>
                </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                    {% trans "Contact Requests" %} ({% trans "Total" %})
                  </div>
                  <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_requests|default:0 }}</div>
                </div>
                <div class="col-auto">
                  <i class="bi bi-envelope-fill fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                    {% trans "Active Offers" %}
                  </div>
                  <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_offers|default:0 }}</div>
                </div>
                <div class="col-auto">
                  <i class="bi bi-briefcase-fill fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                    {% trans "Profile Completion" %}
                  </div>
                  <div class="row no-gutters align-items-center">
                    <div class="col">
                      <div class="progress progress-sm mr-2 mb-2">
                        <div class="progress-bar bg-info" role="progressbar" style="width: {{ completion_percentage }}%"
                             aria-valuenow="{{ completion_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                      </div>
                    </div>
                    <div class="col-auto">
                      <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">{{ completion_percentage }}%</div>
                    </div>
                  </div>
                  <a href="{% url 'services:profile' %}" class="small text-info">{% trans "Complete your profile" %}</a>
                </div>
                <div class="col-auto">
                  <i class="bi bi-person-check fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                    {% trans "Pending Requests" %}
                  </div>
                  <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_requests|default:0 }}</div>
                </div>
                <div class="col-auto">
                  <i class="bi bi-clock-fill fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Contact Requests -->
      <h2 class="h4 mt-4 mb-3">{% trans "Recent Contact Requests" %}</h2>
      <div class="table-responsive mb-4">
        <table class="table table-striped table-sm">
          <thead>
            <tr>
              <th>{% trans "Date" %}</th>
              <th>{% trans "Applicant" %}</th>
              <th>{% trans "Related Offer" %}</th>
              <th>{% trans "Status" %}</th>
              <th>{% trans "Actions" %}</th>
            </tr>
          </thead>
          <tbody>
            {% for request in recent_contact_requests %}
              <tr>
                <td>{{ request.created_at|date:"M d, Y" }}</td>
                <td>{{ request.applicant.user.get_full_name }}</td>
                <td>{{ request.offer.title|default:"-" }}</td>
                <td>
                  {% if request.status == 'pending' %}
                    <span class="badge bg-warning text-dark">{% trans "Pending" %}</span>
                  {% elif request.status == 'accepted' %}
                    <span class="badge bg-success">{% trans "Accepted" %}</span>
                  {% elif request.status == 'rejected' %}
                    <span class="badge bg-danger">{% trans "Rejected" %}</span>
                  {% endif %}
                </td>
                <td>
                  <a href="{% url 'services:contact_request_detail' request.id %}" class="btn btn-sm btn-outline-primary">{% trans "View" %}</a>
                </td>
              </tr>
            {% empty %}
              <tr>
                <td colspan="5" class="text-center py-3">
                  <i class="bi bi-info-circle me-2"></i>
                  {% trans "No recent contact requests" %}
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
        {% if recent_contact_requests %}
          <div class="text-end">
            <a href="{% url 'services:contact_requests' %}" class="btn btn-link">{% trans "View All" %} <i class="bi bi-arrow-right"></i></a>
          </div>
        {% endif %}
      </div>

      <!-- My Offers -->
      <h2 class="h4 mt-4 mb-3">{% trans "My Offers" %}</h2>
      <div class="row row-cols-1 row-cols-md-2 g-4 mb-4">
        {% for offer in service_offers %}
          <div class="col">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex justify-content-between">
                  <h5 class="card-title">{{ offer.title }}</h5>
                  {% if offer.is_active %}
                    <span class="badge bg-success">{% trans "Active" %}</span>
                  {% else %}
                    <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                  {% endif %}
                </div>
                <p class="card-text">{{ offer.description|truncatechars:100 }}</p>
                <div class="d-flex justify-content-between align-items-center mt-3">
                  <div>
                    <span class="fs-5 fw-bold">${{ offer.price }}</span>
                  </div>
                  <div>
                    <a href="{% url 'services:edit_offer' offer.id %}" class="btn btn-sm btn-primary">{% trans "Edit" %}</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        {% empty %}
          <div class="col-12">
            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              {% trans "You don't have any offers yet." %}
              <a href="{% url 'services:create_offer' %}" class="alert-link">{% trans "Create your first offer" %}</a>.
            </div>
          </div>
        {% endfor %}
        {% if service_offers %}
          <div class="col-12 text-end mt-3">
            <a href="{% url 'services:offers' %}" class="btn btn-link">{% trans "View All" %} <i class="bi bi-arrow-right"></i></a>
          </div>
        {% endif %}
      </div>

      <!-- Recommended Applicants -->
      <h2 class="h4 mt-4 mb-3">{% trans "Recommended Applicants" %}</h2>
      <div class="row">
        {% if subscription.is_active %}
          {% for applicant in recommended_applicants %}
            <div class="col-md-4 mb-4">
              <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                  <div class="text-center mb-3">
                    {% if applicant.profile_picture %}
                      <img src="{{ applicant.profile_picture.url }}" class="rounded-circle" width="80" height="80" alt="{{ applicant.user.get_full_name }}">
                    {% else %}
                      <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center mx-auto" style="width: 80px; height: 80px;">
                        <i class="bi bi-person-fill" style="font-size: 2rem;"></i>
                      </div>
                    {% endif %}
                    <h5 class="card-title mt-3">{{ applicant.user.get_full_name }}</h5>
                    <p class="text-muted">{{ applicant.job_title|default:"-" }}</p>
                  </div>

                  <div class="mb-3">
                    <h6 class="fw-bold">{% trans "Skills" %}</h6>
                    {% for skill in applicant.skills.all|slice:":3" %}
                      <span class="badge bg-light text-dark me-1 mb-1">{{ skill.name }}</span>
                    {% empty %}
                      <p class="text-muted small">{% trans "No skills listed" %}</p>
                    {% endfor %}
                    {% if applicant.skills.all|length > 3 %}
                      <span class="badge bg-light text-dark">+{{ applicant.skills.all|length|sub:3 }}</span>
                    {% endif %}
                  </div>

                  <div class="d-grid">
                    <a href="{% url 'services:applicant_detail' applicant.id %}" class="btn btn-outline-primary btn-sm">{% trans "View Profile" %}</a>
                  </div>
                </div>
              </div>
            </div>
          {% empty %}
            <div class="col-12">
              <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                {% trans "No recommended applicants at this time. Check back later!" %}
              </div>
            </div>
          {% endfor %}
          {% if recommended_applicants %}
            <div class="col-12 text-end mt-3">
              <a href="{% url 'services:applicants' %}" class="btn btn-link">{% trans "Browse All Applicants" %} <i class="bi bi-arrow-right"></i></a>
            </div>
          {% endif %}
        {% else %}
          <div class="col-12">
            <div class="alert alert-warning">
              <i class="bi bi-exclamation-triangle me-2"></i>
              {% trans "Subscribe to a plan to browse and connect with applicants." %}
              <a href="{% url 'services:subscription' %}" class="alert-link">{% trans "Subscribe now" %}</a>.
            </div>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  // Simple Chart.js implementation could go here
  // For dashboard stats visualization
</script>
{% endblock %}