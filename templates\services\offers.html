{% extends 'base.html' %}
{% load static %}

{% block title %}My Offers - Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row mb-4">
    <div class="col-md-8">
      <h1 class="mb-3">My Offers</h1>
      <p class="text-muted">Manage your job offers and opportunities.</p>
    </div>
    <div class="col-md-4 text-md-end">
      <a href="{% url 'services:create_offer' %}" class="btn btn-primary">
        <i class="bi bi-plus-lg"></i> Create New Offer
      </a>
      <a href="{% url 'services:dashboard' %}" class="btn btn-outline-primary ms-2">
        <i class="bi bi-arrow-left"></i> Dashboard
      </a>
    </div>
  </div>

  <!-- Offers List -->
  <div class="card">
    <div class="card-body">
      {% if page_obj %}
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Title</th>
                <th>Description</th>
                <th>Price</th>
                <th>Status</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for offer in page_obj %}
                <tr>
                  <td>{{ offer.title }}</td>
                  <td>{{ offer.description|truncatechars:50 }}</td>
                  <td>${{ offer.price }}</td>
                  <td>
                    {% if offer.is_active %}
                      <span class="badge bg-success">Active</span>
                    {% else %}
                      <span class="badge bg-secondary">Inactive</span>
                    {% endif %}
                  </td>
                  <td>{{ offer.created_at|date:"M d, Y" }}</td>
                  <td>
                    <div class="btn-group">
                      <a href="{% url 'services:ai_candidate_matching' offer.id %}" class="btn btn-sm btn-success" title="AI Candidate Matching">
                        <i class="bi bi-robot"></i>
                      </a>
                      <a href="{% url 'services:setup_job_requirements' offer.id %}" class="btn btn-sm btn-info" title="Configure Requirements">
                        <i class="bi bi-gear"></i>
                      </a>
                      <a href="{% url 'services:edit_offer' offer.id %}" class="btn btn-sm btn-outline-primary" title="Edit Offer">
                        <i class="bi bi-pencil"></i>
                      </a>
                      <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteOfferModal{{ offer.id }}" title="Delete Offer">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>

                    <!-- Delete Modal -->
                    <div class="modal fade" id="deleteOfferModal{{ offer.id }}" tabindex="-1" aria-labelledby="deleteOfferModalLabel{{ offer.id }}" aria-hidden="true">
                      <div class="modal-dialog">
                        <div class="modal-content">
                          <div class="modal-header">
                            <h5 class="modal-title" id="deleteOfferModalLabel{{ offer.id }}">Delete Offer</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                          </div>
                          <div class="modal-body">
                            <p>Are you sure you want to delete the offer "{{ offer.title }}"? This action cannot be undone.</p>
                          </div>
                          <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <form method="post" action="{% url 'services:delete_offer' offer.id %}">
                              {% csrf_token %}
                              <button type="submit" class="btn btn-danger">Delete</button>
                            </form>
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
          <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
              {% if page_obj.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?page=1" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                  </a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                  </a>
                </li>
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% endif %}

              {% for i in page_obj.paginator.page_range %}
                {% if page_obj.number == i %}
                  <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                  <li class="page-item">
                    <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                  </li>
                {% endif %}
              {% endfor %}

              {% if page_obj.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                  </a>
                </li>
              {% endif %}
            </ul>
          </nav>
        {% endif %}
      {% else %}
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i> You haven't created any offers yet.
        </div>
        <div class="text-center mt-4">
          <a href="{% url 'services:create_offer' %}" class="btn btn-primary">
            <i class="bi bi-plus-lg"></i> Create Your First Offer
          </a>
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
